// NOTE:
//  LoRa MOSI and E-Paper EPD_DIN are on same pin
// Arduino IDE:
//  TTGO LoRa32-OLED
//  115200 BaudRate
//      80 MHz

#include <Esp.h>

#define uS_TO_S_FACTOR                                                         \
  1000000ULL              // Conversion factor for micro seconds to seconds
#define TIME_TO_SLEEP 60 // Time ESP32 will go to sleep (in seconds)

#include "my_epaper.h"
#include "my_io.h"
#include "my_lora.h"
#include "my_nfc.h"
#include "my_oled.h"

void setup() {
  IOSetup();
  EPDSetup();
  OLEDSetup();
  // LoRaSetup();
  // NFCSetup();

  // Test audio functionality (uncomment to enable)
  // testAudioPlayback();      // Test simple melody
  testGeneratedAudio();     // Test MP3-converted audio

  // Configure wake up sources
  // Timer wake-up (backup - wake up every X seconds)
  esp_sleep_enable_timer_wakeup(uS_TO_S_FACTOR * TIME_TO_SLEEP);
  //  Wake only on encoder rotation
  setupEncoderRotationWakeup();
}

void loop() {
  // Check for encoder input before updating displays
  handleEncoderInput();

  EPDUpdate();
  OLEDUpdate();
  NFCUpdate();
  // LoRaUpdate();
  // NFCSetup();

  IOEnd();

  Serial.println("Going to sleep now for " + String(TIME_TO_SLEEP) + " seconds");
  esp_deep_sleep_start();
}
