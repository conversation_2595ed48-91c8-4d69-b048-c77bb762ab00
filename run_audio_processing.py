#!/usr/bin/env python3
"""
Wrapper script to run audio processing with the correct Python environment.
This script ensures that librosa and numpy are available.
"""

import sys
import subprocess
from pathlib import Path


def run_audio_processing():
    """Run audio processing using the correct Python environment."""
    print("🎵 Running audio processing...")
    
    # Try to run with uv first (if available)
    try:
        result = subprocess.run([
            "uv", "run", "python", "-m", "audio_processing.preprocess_audio_files"
        ], cwd=Path.cwd(), capture_output=True, text=True)
        
        if result.returncode == 0:
            print(result.stdout)
            print("✅ Audio processing completed successfully!")
            return
        else:
            print(f"uv run failed: {result.stderr}")
    except FileNotFoundError:
        print("uv not found, trying direct python...")
    
    # Fallback to direct python
    try:
        result = subprocess.run([
            sys.executable, "-m", "audio_processing.preprocess_audio_files"
        ], cwd=Path.cwd(), capture_output=True, text=True)
        
        if result.returncode == 0:
            print(result.stdout)
            print("✅ Audio processing completed successfully!")
            return
        else:
            print(f"❌ Audio processing failed: {result.stderr}")
    except Exception as e:
        print(f"❌ Error running audio processing: {e}")
    
    # If all else fails, show installation instructions
    print("\n❌ Audio processing failed!")
    print("Please install the required dependencies:")
    print("  uv add librosa numpy")
    print("  # or")
    print("  pip install librosa numpy")


if __name__ == "__main__":
    run_audio_processing()
