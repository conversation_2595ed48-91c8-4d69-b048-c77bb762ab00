import os
from pathlib import Path
from typing import Optional, Any
from .core import (
    process_audio_file,
    clean_audio_destination_folder,
    load_master_audio_template
)


def preprocess_audio_files(source: Optional[Any] = None, target: Optional[Any] = None, env: Optional[Any] = None) -> None:
    """Process MP3 files from data-unprocessed to C++ headers."""
    src_folder = Path("data-unprocessed")
    dst_folder = Path("include/audio")

    src_folder.mkdir(parents=True, exist_ok=True)
    dst_folder.mkdir(parents=True, exist_ok=True)

    clean_audio_destination_folder(dst_folder)
    print(f"Processing MP3 files from {src_folder} to {dst_folder}")
    print("Converting audio to buzzer-compatible frequency/duration arrays")

    # Collect audio info for master header
    audio_entries = []
    enum_entries = []
    include_entries = []

    for file_path in src_folder.glob("*.mp3"):
        file_name = file_path.stem
        
        # Clean up filename for C++ compatibility
        clean_name = file_name.replace("-", "_").replace(" ", "_").replace("#", "").replace("[", "").replace("]", "").replace(".", "_").replace("(", "").replace(")", "")

        # Ensure the name starts with a letter (C++ requirement)
        if clean_name and clean_name[0].isdigit():
            clean_name = "track_" + clean_name
        
        output_file = dst_folder / (clean_name + ".h")
        print(f"Processing {file_path} -> {output_file}")

        # Process file and get result info
        try:
            result = process_audio_file(file_path, output_file, max_duration_seconds=30.0)
            print()

            array_name = clean_name
            audio_entries.append(f'    {{ {array_name}_frequencies, {array_name}_durations, {array_name}_note_count, {array_name}_total_duration_ms }}')
            enum_entries.append(f'    AUDIO_{array_name.upper()}')
            include_entries.append(f'#include "{clean_name}.h"')
            
        except Exception as e:
            print(f"Error processing {file_path}: {e}")
            print()

    if not include_entries:
        print("No audio files were successfully processed.")
        return

    # Write master header using template
    master_header = dst_folder / "audio_tracks.h"
    master_template = load_master_audio_template()

    # Debug output
    print(f"DEBUG: Creating master audio header with {len(include_entries)} includes:")
    for inc in include_entries:
        print(f"  {inc}")

    header_content = master_template.format(
        includes="\n".join(include_entries),
        enum_entries=",\n".join(enum_entries),
        audio_entries=",\n".join(audio_entries)
    )

    # Only overwrite if content changed
    if not master_header.exists() or master_header.read_text(encoding="utf-8") != header_content:
        with open(master_header, "w", encoding="utf-8") as f:
            f.write(header_content)
        print(f"Master audio header generated: {master_header}")
    else:
        print(f"Master audio header unchanged: {master_header}")
    print("All MP3 files have been processed and saved as C++ headers.")


# Direct execution support
if __name__ == "__main__":
    preprocess_audio_files()
