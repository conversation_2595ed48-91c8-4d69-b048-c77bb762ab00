"""
Core audio processing functions for converting MP3 files to buzzer-compatible format.
"""

import os
import numpy as np
from pathlib import Path
from typing import <PERSON><PERSON>, <PERSON><PERSON>, List
import librosa
import librosa.display


def get_script_dir() -> Path:
    """Get the directory of this script, fallback to cwd if __file__ is not defined."""
    try:
        return Path(__file__).parent
    except NameError:
        return Path(os.getcwd())


def get_template_path(template_name: str) -> Path:
    """Get the path to a template file, handling different execution contexts."""
    script_dir = get_script_dir()

    # First try: template in same directory as script (normal case)
    template_path = script_dir / template_name
    if template_path.exists():
        return template_path

    # Second try: template in audio_processing subdirectory (if running from root)
    template_path = script_dir / "audio_processing" / template_name
    if template_path.exists():
        return template_path

    # Third try: look in current working directory
    template_path = Path(os.getcwd()) / "audio_processing" / template_name
    if template_path.exists():
        return template_path

    # Fallback: original path (will cause error if not found)
    return script_dir / template_name


def load_cpp_audio_template() -> str:
    """Load the C++ audio template."""
    template_path = get_template_path("cpp_audio_template.txt")
    with open(template_path, "r") as f:
        return f.read()


def load_master_audio_template() -> str:
    """Load the C++ master audio header template."""
    template_path = get_template_path("cpp_master_audio_header_template.txt")
    with open(template_path, "r") as f:
        return f.read()


def extract_melody_from_audio(audio_path: Path, target_sample_rate: int = 22050,
                             hop_length: int = 512) -> Tuple[List[float], List[float]]:
    """
    Extract melody (fundamental frequencies) and timing from audio file.

    Args:
        audio_path: Path to the audio file
        target_sample_rate: Target sample rate for processing
        hop_length: Number of samples between successive frames

    Returns:
        Tuple of (frequencies, durations) lists
    """
    print(f"Loading audio file: {audio_path}")

    # Load audio file
    y, sr = librosa.load(audio_path, sr=target_sample_rate)

    # Extract fundamental frequency using piptrack
    pitches, magnitudes = librosa.piptrack(y=y, sr=sr, hop_length=hop_length, threshold=0.1)

    # Extract the most prominent frequency for each frame
    frequencies = []
    for t in range(pitches.shape[1]):
        index = magnitudes[:, t].argmax()
        pitch = pitches[index, t]
        frequencies.append(pitch if pitch > 0 else 0)  # 0 for silence/rest

    # Calculate frame duration in milliseconds
    frame_duration_ms = (hop_length / sr) * 1000

    # Simplify by grouping similar frequencies and durations
    simplified_freqs, simplified_durations = simplify_melody(frequencies, frame_duration_ms)

    return simplified_freqs, simplified_durations


def simplify_melody(frequencies: List[float], frame_duration_ms: float, 
                   freq_tolerance: float = 10.0, min_duration_ms: float = 50.0) -> Tuple[List[float], List[float]]:
    """
    Simplify melody by grouping similar consecutive frequencies and extending short notes.
    
    Args:
        frequencies: List of frequencies in Hz
        frame_duration_ms: Duration of each frame in milliseconds
        freq_tolerance: Tolerance for grouping similar frequencies (Hz)
        min_duration_ms: Minimum duration for a note (ms)
    
    Returns:
        Tuple of (simplified_frequencies, durations) lists
    """
    if not frequencies:
        return [], []
    
    simplified_freqs = []
    simplified_durations = []
    
    current_freq = frequencies[0]
    current_duration = frame_duration_ms
    
    for freq in frequencies[1:]:
        # Check if frequency is similar to current (within tolerance)
        if abs(freq - current_freq) <= freq_tolerance or (freq == 0 and current_freq == 0):
            # Extend current note
            current_duration += frame_duration_ms
        else:
            # Save current note if it meets minimum duration
            if current_duration >= min_duration_ms:
                simplified_freqs.append(current_freq)
                simplified_durations.append(current_duration)
            
            # Start new note
            current_freq = freq
            current_duration = frame_duration_ms
    
    # Don't forget the last note
    if current_duration >= min_duration_ms:
        simplified_freqs.append(current_freq)
        simplified_durations.append(current_duration)
    
    return simplified_freqs, simplified_durations


def quantize_frequencies_for_buzzer(frequencies: List[float]) -> List[int]:
    """
    Quantize frequencies to buzzer-friendly values.
    ESP32 buzzer works well with frequencies between 100Hz and 5000Hz.
    """
    quantized = []
    for freq in frequencies:
        if freq <= 0:
            quantized.append(0)  # Rest/silence
        elif freq < 100:
            quantized.append(100)  # Minimum buzzer frequency
        elif freq > 5000:
            quantized.append(5000)  # Maximum buzzer frequency
        else:
            # Round to nearest 10Hz for cleaner values
            quantized.append(int(round(freq / 10) * 10))
    
    return quantized


def frequencies_to_hex(frequencies: List[int]) -> str:
    """Convert frequency list to hex string representation."""
    freq_str = ""
    for i, freq in enumerate(frequencies):
        freq_str += f"{freq}, "
        if (i + 1) % 8 == 0:
            freq_str += "\n    "
    return freq_str.rstrip()


def durations_to_hex(durations: List[float]) -> str:
    """Convert duration list to hex string representation (in milliseconds)."""
    duration_str = ""
    for i, duration in enumerate(durations):
        duration_ms = int(round(duration))
        duration_str += f"{duration_ms}, "
        if (i + 1) % 8 == 0:
            duration_str += "\n    "
    return duration_str.rstrip()


def process_audio_file(input_path: Path, output_path: Path, max_duration_seconds: float = 30.0) -> str:
    """
    Process audio file and convert to buzzer-compatible format.

    Args:
        input_path: Path to input MP3 file
        output_path: Path for output header file (without extension)
        max_duration_seconds: Maximum duration to process (to avoid huge arrays)

    Returns:
        String describing the processing result
    """
    print(f"Processing audio file: {input_path}")
    
    # Extract melody from audio
    frequencies, durations = extract_melody_from_audio(input_path)
    
    # Limit duration to prevent huge arrays
    total_duration = 0
    limited_frequencies = []
    limited_durations = []
    
    for freq, dur in zip(frequencies, durations):
        if total_duration + dur/1000 > max_duration_seconds:
            # Add partial duration to reach max_duration_seconds
            remaining_time = (max_duration_seconds - total_duration) * 1000
            if remaining_time > 50:  # Only add if at least 50ms remaining
                limited_frequencies.append(freq)
                limited_durations.append(remaining_time)
            break
        
        limited_frequencies.append(freq)
        limited_durations.append(dur)
        total_duration += dur/1000
    
    frequencies = limited_frequencies
    durations = limited_durations
    
    if not frequencies:
        print(f"\t{input_path.name}: No melody extracted!")
        return "No melody found"
    
    # Quantize frequencies for buzzer
    quantized_frequencies = quantize_frequencies_for_buzzer(frequencies)
    
    # Convert to string representations
    freq_str = frequencies_to_hex(quantized_frequencies)
    duration_str = durations_to_hex(durations)
    
    note_count = len(frequencies)
    total_duration_ms = sum(durations)
    
    # Write C++ header file using template
    array_name = output_path.stem.replace("-", "_").replace(" ", "_").replace("#", "").replace("[", "").replace("]", "").replace(".", "_").replace("(", "").replace(")", "")

    # Ensure the array name starts with a letter (C++ requirement)
    if array_name and array_name[0].isdigit():
        array_name = "track_" + array_name
    cpp_template = load_cpp_audio_template()
    header_content = cpp_template.format(
        array_name=array_name,
        note_count=note_count,
        frequency_data=freq_str,
        duration_data=duration_str,
        meta_data=f"Converted {input_path.name} - {note_count} notes, {total_duration_ms/1000:.1f}s duration",
        total_duration_ms=int(total_duration_ms)
    )
    
    with open(output_path.with_suffix(".h"), "w") as f:
        f.write(header_content)

    print(f"\t{input_path.name}: extracted {note_count} notes, {total_duration_ms/1000:.1f}s total, exported as {output_path.with_suffix('.h').name}")
    
    return f"Converted to {note_count} notes"


def clean_audio_destination_folder(dst_folder: Path):
    """Clean up audio destination folder."""
    print(f"Cleaning up audio destination folder: {dst_folder}")

    # Clean up header files with error handling
    for header_file in dst_folder.glob("*.h"):
        try:
            header_file.unlink()
            print(f"Removed: {header_file}")
        except PermissionError:
            print(f"Warning: Could not remove {header_file} (permission denied)")
        except Exception as e:
            print(f"Warning: Could not remove {header_file}: {e}")

    print("Audio destination folder cleaned successfully")
