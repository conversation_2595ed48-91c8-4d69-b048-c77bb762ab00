// This file is auto-generated by preprocess_audio_files.py. Do not edit this manually.
#pragma once

{includes}

// Audio track enumeration
enum AudioTrack {{
{enum_entries}
}};

// Audio track structure
struct AudioTrackInfo {{
    const int* frequencies;
    const int* durations;
    int note_count;
    int total_duration_ms;
}};

// Audio track array
constexpr AudioTrackInfo audio_tracks[] = {{
{audio_entries}
}};

// Helper function to play an audio track
inline void playAudioTrack(AudioTrack track) {{
    if (track >= 0 && track < sizeof(audio_tracks) / sizeof(audio_tracks[0])) {{
        const AudioTrackInfo& info = audio_tracks[track];
        for (int i = 0; i < info.note_count; i++) {{
            int frequency = info.frequencies[i];
            int duration = info.durations[i];
            
            if (frequency > 0) {{
                tone(12, frequency, duration);  // Pin 12 is BUZZER
                delay(duration);
                noTone(12);
            }} else {{
                delay(duration);  // Rest/silence
            }}
        }}
    }}
}}
