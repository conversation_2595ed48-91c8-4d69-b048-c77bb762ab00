// This file is auto-generated by preprocess_audio_files.py. Do not edit this manually.
#pragma once

/*
{meta_data}
Total duration: {total_duration_ms}ms
*/

constexpr int {array_name}_note_count = {note_count};
constexpr int {array_name}_total_duration_ms = {total_duration_ms};

// Frequencies in Hz (0 = rest/silence)
constexpr int {array_name}_frequencies[] = {{
    {frequency_data}
}};

// Durations in milliseconds
constexpr int {array_name}_durations[] = {{
    {duration_data}
}};
